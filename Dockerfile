FROM rustlang/rust:nightly-bookworm as build

WORKDIR /app

# Runtime dependencies
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    gcc llvm clang libtool protobuf-compiler && \
    rm -rf /var/lib/apt/lists/*

# Install Rust nightly version
RUN rustup toolchain install nightly-2025-02-11
RUN rustup default nightly-2025-02-11

# Copy workspace configuration and dependencies first
COPY Cargo.toml Cargo.lock ./

# Build dependencies only
RUN mkdir src && \
    echo "fn main() {}" > src/main.rs && \
    cargo build --release && \
    rm -rf src

# Now copy the real source code
COPY . .

# Build the actual project
RUN RUSTFLAGS="-C target-cpu=native" cargo build --release && \
    cp target/release/subnet-dpn-websocket /usr/local/bin/websocket

FROM ubuntu:22.04
RUN apt-get update && \
    apt-get -y install libssl3 ca-certificates && \
    rm -rf /var/lib/apt/lists/*
COPY --from=build /usr/local/bin/websocket /usr/local/bin/websocket
WORKDIR /

ENTRYPOINT ["websocket"]