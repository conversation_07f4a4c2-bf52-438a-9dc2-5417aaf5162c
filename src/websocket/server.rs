use anyhow::Result;
use chrono::Utc;
use reqwest;
use serde_json::json;
use std::{collections::HashMap, net::SocketAddr, sync::Arc};
use tokio::{
    net::{TcpListener, TcpStream},
    sync::Mutex,
};
use tokio_tungstenite::accept_async;
use tracing::{debug, error, info, warn};
use tungstenite::Message;

use crate::{
    config::Config,
    messaging::{
        events::{MsgQueueEvent, SESSION_EVENT_QUEUE, SESSION_STATS_QUEUE, SessionEvent},
        rabbitmq::RabbitMQClient,
    },
    session::manager::SessionManager,
    websocket::connection::{Connection, ConnectionId},
};

type ConnectionPool = Arc<Mutex<HashMap<ConnectionId, Connection>>>;

pub struct WebSocketServer {
    bind_address: SocketAddr,
    connections: ConnectionPool,
    session_manager: Arc<SessionManager>,
    rabbitmq_client: Arc<RabbitMQClient>,
    update_interval_seconds: u64,
    connection_timeout_seconds: u64,
    config: Arc<Config>,
}

impl WebSocketServer {
    pub fn new(
        bind_address: SocketAddr,
        session_manager: SessionManager,
        rabbitmq_client: RabbitMQClient,
        update_interval_seconds: u64,
        connection_timeout_seconds: u64,
    ) -> Result<Self, anyhow::Error> {
        // Load configuration
        let config = Config::from_env()?;

        Ok(WebSocketServer {
            bind_address,
            connections: Arc::new(Mutex::new(HashMap::new())),
            session_manager: Arc::new(session_manager),
            rabbitmq_client: Arc::new(rabbitmq_client),
            update_interval_seconds,
            connection_timeout_seconds,
            config: Arc::new(config),
        })
    }

    fn get_update_interval(&self) -> tokio::time::Duration {
        tokio::time::Duration::from_secs(self.update_interval_seconds)
    }

    fn get_cleanup_interval(&self) -> tokio::time::Duration {
        tokio::time::Duration::from_secs(self.connection_timeout_seconds)
    }

    /// Log the current count of connected providers
    async fn log_connection_count(connections: &ConnectionPool) {
        let conns = connections.lock().await;
        let count = conns.len();
        info!("Current connected providers count: {}", count);

        // If you want more detailed logging, you can include the provider addresses
        if count > 0 {
            let providers: Vec<&String> = conns.keys().collect();
            debug!("Connected providers: {:?}", providers);
        }
    }

    pub async fn run(&self) -> Result<()> {
        let listener = TcpListener::bind(self.bind_address).await?;
        info!("WebSocket server listening on {}", self.bind_address);

        //Start the periodic update task
        let update_connections = self.connections.clone();
        let update_rabbitmq = self.rabbitmq_client.clone();
        let update_session_manager = self.session_manager.clone();
        let update_interval = self.update_interval_seconds;
        let cleanup_interval = self.connection_timeout_seconds;
        tokio::spawn(Self::periodic_updates(
            update_connections,
            update_rabbitmq,
            update_session_manager,
            update_interval,
            cleanup_interval,
        ));

        // Start the cleanup task
        let cleanup_connections = self.connections.clone();
        tokio::spawn(Self::cleanup_inactive_connections(cleanup_connections));

        // Start clean up cache memory for finished session
        let cleanup_session_manager = self.session_manager.clone();
        tokio::spawn(async move {
            let cleanup_interval = tokio::time::Duration::from_secs(60); // Run every 60 seconds, TODO: move value to config
            let mut interval = tokio::time::interval(cleanup_interval);

            loop {
                interval.tick().await;
                if let Err(e) = cleanup_session_manager.cleanup_finished_sessions().await {
                    error!("Failed to clean up finished sessions: {}", e);
                }
            }
        });

        // Accept and handle connections
        loop {
            match listener.accept().await {
                Ok((stream, addr)) => {
                    let connections = self.connections.clone();
                    let session_manager = self.session_manager.clone();
                    let config = self.config.clone();

                    tokio::spawn(async move {
                        Self::handle_connection(stream, addr, connections, session_manager, config)
                            .await;
                    });
                }
                Err(e) => {
                    error!("Failed to accept connection: {}", e);
                }
            }
        }
        // Ok(())
    }

    async fn handle_connection(
        stream: TcpStream,
        addr: SocketAddr,
        connections: ConnectionPool,
        session_manager: Arc<SessionManager>,
        config: Arc<Config>,
    ) {
        // Connection handling logic
        info!("New TCP connection from: {}", addr);

        // Perform WebSocket handshake
        let ws_stream = match accept_async(stream).await {
            Ok(ws) => ws,
            Err(e) => {
                error!("WebSocket handshake failed for {}: {}", addr, e);
                return;
            }
        };

        // Create new connection object
        let mut connection = match Connection::new(ws_stream, addr).await {
            Ok(conn) => conn,
            Err(e) => {
                error!("Failed to accept WebSocket connection for {}: {}", addr, e);
                return;
            }
        };

        info!("WebSocket connection established with: {}", addr);

        // Authenticate the connection
        let provider_addr = match authenticate_connection(&mut connection, &config).await {
            Ok(provider_addr) => {
                info!("Authentication successful for provider: {}", provider_addr.clone());
                provider_addr
            },
            Err(e) => {
                error!("Authentication failed for {}: {}", addr, e);
                let _ = connection
                    .send_message(
                        "error",
                        json!({
                            "message": "Authentication failed",
                            "code": "AUTH_FAILED"
                        }),
                    )
                    .await;
                let _ = connection.close().await;
                return;
            }
        };

        connection.set_provider_addr(provider_addr.clone());

        // Add connection to the pool
        {
            let mut conns = connections.lock().await;

            // Check if a connection for this provider already exists
            if let Some(existing_conn) = conns.get_mut(&provider_addr) {
                // Send a message to the existing connection that it's being replaced
                let _ = existing_conn
                    .send_message(
                        "system",
                        json!({
                            "message": "Your connection was replaced by a new login",
                            "code": "CONNECTION_REPLACED"
                        }),
                    )
                    .await;

                // Close the existing connection
                let _ = existing_conn.close().await;
            }

            // Add the new connection
            conns.insert(provider_addr.clone(), connection);

            // Log the current count of connected providers
            let count = conns.len();
            info!("Provider authenticated: {}. Total connected providers: {}", provider_addr, count);
        }

        // Send initial session data
        if let Err(e) =
            send_initial_sessions(&provider_addr, connections.clone(), session_manager.clone())
                .await
        {
            error!(
                "Failed to send initial sessions to {}: {}",
                provider_addr, e
            );
        }
        // Process messages from this client
        process_client_messages(provider_addr, connections.clone(), session_manager.clone()).await;
    }

    async fn periodic_updates(
        connections: ConnectionPool,
        rabbitmq_client: Arc<RabbitMQClient>,
        session_manager: Arc<SessionManager>,
        update_interval_seconds: u64,
        connection_timeout_seconds: u64,
    ) {
        // Set up the RabbitMQ consumers
        let mut session_events_rx = match rabbitmq_client.start_consuming(SESSION_EVENT_QUEUE).await
        {
            Ok(rx) => rx,
            Err(e) => {
                error!(
                    "Failed to set up RabbitMQ consumer for session events: {}",
                    e
                );
                return;
            }
        };

        let mut stats_rx = match rabbitmq_client.start_consuming(SESSION_STATS_QUEUE).await {
            Ok(rx) => rx,
            Err(e) => {
                error!("Failed to set up RabbitMQ consumer for stats: {}", e);
                return;
            }
        };

        let update_interval = tokio::time::Duration::from_secs(update_interval_seconds);
        let _cleanup_interval = tokio::time::Duration::from_secs(connection_timeout_seconds);

        let mut update_timer = tokio::time::interval(update_interval);
        let _cleanup_timer = tokio::time::interval(_cleanup_interval);

        // Sequence number for messages
        let mut _sequence_number = 0u64;

        loop {
            tokio::select! {
                // Handle session events
                Some(event) = session_events_rx.recv() => {
                    match event {
                        MsgQueueEvent::SessionEvent(session_event) => {
                            match &session_event {
                                SessionEvent::SessionCreated(_) => {
                                    // info!("Received session created event: {:?}", session_event);
                                    if let Err(e) = session_manager.handle_session_created(&session_event).await {
                                        error!("Failed to handle session created event: {}", e);
                                    }
                                },
                                SessionEvent::SessionTerminated(_) => {
                                    // info!("Received session terminated event: {:?}", session_event);
                                    if let Err(e) = session_manager.handle_session_terminated(&session_event).await {
                                        error!("Failed to handle session terminated event: {}", e);
                                    }
                                }
                            }
                        },
                        _ => {
                            warn!("Received unexpected event type in session events queue");
                        }
                    }
                },

                // Handle stats updates
                Some(stats_event) = stats_rx.recv() => {
                    match stats_event {
                        MsgQueueEvent::SessionStats(stats) => {
                            if let Err(e) = session_manager.handle_session_stats(&stats).await {
                                error!("Failed to handle session stats update: {}", e);
                            }
                        },
                        _ => {
                            warn!("Received unexpected event type in stats queue");
                        }
                    }
                },

                // Send periodic updates
                _ = update_timer.tick() => {
                    // info!("Sending periodic updates (tick)");
                    // Increment sequence number for tracking updates
                    _sequence_number += 1;

                    // Log the current count of connected providers
                    WebSocketServer::log_connection_count(&connections).await;

                    // Get aggregated updates from session manager
                    // info!("Getting aggregated updates from session manager");
                    let provider_updates = session_manager.get_aggregated_updates().await;
                    // debug!("Found {} providers with updates", provider_updates.len());

                    if !provider_updates.is_empty() {
                        debug!("Sending updates to {} providers", provider_updates.len());

                        // Lock connections once to avoid multiple lock/unlock operations
                        // info!("Acquiring lock on connections map for updates");
                        let mut conns = connections.lock().await;
                        // info!("Acquired lock on connections map for updates");

                        // Send updates to each provider
                        for (provider_addr, updates) in provider_updates {
                            if let Some(connection) = conns.get_mut(&provider_addr) {
                                // Prepare update message with sessions and their stats
                                let mut session_updates = Vec::new();

                                for session in &updates.updated_sessions {
                                    let session_data = serde_json::to_value(session).unwrap_or_default();
                                    // No longer adding stats to the session data
                                    session_updates.push(session_data);
                                }

                                // Send the update message
                                if !session_updates.is_empty() {
                                    debug!("Connection details for provider {}: last_active={}",
                                          provider_addr, connection.get_last_active());

                                    // Wrap session updates in a "sessions" object to match the expected format
                                    let payload = json!({
                                        "sessions": session_updates
                                    });
                                    match connection.send_message("session_update", payload).await {
                                        Ok(_) => {
                                            debug!("Successfully sent session_update to provider {}", provider_addr);
                                        },
                                        Err(e) => {
                                            error!("Failed to send session_update to provider {}: {}", provider_addr, e);
                                            // Don't remove the connection here - it will be removed by connection cleanup
                                        }
                                    }
                                    // info!("Finished sending session_update to provider {}", provider_addr);
                                }
                            }
                        }
                        // info!("Releasing lock on connections map after sending updates");
                    }
                },

            }
        }
    }

    async fn cleanup_inactive_connections(connections: ConnectionPool) {
        debug!("Starting inactive connection cleanup task");
        let cleanup_interval = tokio::time::Duration::from_secs(60); // Run every 60 seconds
        let mut interval = tokio::time::interval(cleanup_interval);
        let timeout_seconds = 300; // 5 minutes timeout

        loop {
            // info!("Waiting for next cleanup interval");
            interval.tick().await;

            let now = Utc::now().timestamp();

            // Lock connections map
            let mut conns = connections.lock().await;

            // Collect inactive connections
            let inactive_providers: Vec<String> = conns
                .iter()
                .filter(|(_, conn)| now - conn.get_last_active() > timeout_seconds)
                .map(|(id, _)| id.clone())
                .collect();
            debug!("Found {} inactive connections", inactive_providers.len());

            // Remove inactive connections
            for provider_addr in &inactive_providers {
                debug!(
                    "Removing inactive connection for provider: {}",
                    provider_addr
                );

                // Try to send a close message before removing
                if let Some(conn) = conns.get_mut(provider_addr) {
                    let _ = conn.close().await; // Ignore errors, we're removing it anyway
                }

                conns.remove(provider_addr);
            }

            if !inactive_providers.is_empty() {
                info!("Removed {} inactive connections", inactive_providers.len());

                // Log the current count of connected providers after removal
                let count = conns.len();
                info!("Total connected providers after cleanup: {}", count);
            }
        }
    }
}

/// Helper function to send initial session data to a newly connected provider
///
async fn send_initial_sessions(
    provider_addr: &str,
    connections: ConnectionPool,
    session_manager: Arc<SessionManager>,
) -> Result<(), anyhow::Error> {
    info!("Sending initial sessions to {}", provider_addr);
    // Get active sessions for this provider
    let active_sessions = session_manager
        .get_active_sessions_for_provider(provider_addr)
        .await?;

    info!("Found {} active sessions for provider {}", active_sessions.len(), provider_addr);

    if active_sessions.is_empty() {
        return Ok(());
    }

    // Convert sessions to JSON values
    let mut session_updates = Vec::new();
    for session in active_sessions {
        let session_data = serde_json::to_value(&session)?;
        session_updates.push(session_data);
    }
    // Send the initial data message

    let mut conns = connections.lock().await;

    if let Some(connection) = conns.get_mut(provider_addr) {
        connection
            .send_message(
                "sessions",
                json!({
                    "sessions": session_updates
                }),
            )
            .await?;
    }

    Ok(())
}

/// Handle a client message and return an optional response
///
async fn process_client_messages(
    provider_addr: String,
    connections: ConnectionPool,
    session_manager: Arc<SessionManager>,
) {
    debug!("Starting to process messages for provider: {}", provider_addr);

    // Create a timeout for receiving messages - use a very short timeout to allow other tasks to run
    let timeout_duration = tokio::time::Duration::from_millis(10); // 10ms timeout

    loop {
        // Check if the connection exists
        {
            let conns = connections.lock().await;
            if !conns.contains_key(&provider_addr) {
                info!("Connection no longer exists for provider: {}", provider_addr);
                break;
            }
        }

        // debug!("Waiting for message from provider: {}", provider_addr);

        // Create a future that will complete when a message is received or the timeout expires
        let receive_future = async {
            // info!("Acquiring lock on connections map for receiving message from provider: {}", provider_addr);
            let mut conns = connections.lock().await;
            // info!("Acquired lock on connections map for receiving message from provider: {}", provider_addr);

            if let Some(connection) = conns.get_mut(&provider_addr) {
                let result = connection.receive().await;
                debug!("Received message result for provider: {}: {:?}", provider_addr, result.is_some());
                result
            } else {
                // Connection was removed between checks
                debug!("Connection was removed for provider: {}", provider_addr);
                None
            }
        };

        // Wait for the receive future or the timeout
        let msg_result = tokio::select! {
            result = receive_future => {
                debug!("Received message from provider: {} before timeout", provider_addr);
                result
            },
            _ = tokio::time::sleep(timeout_duration) => {
                // Timeout expired, continue the loop to allow other tasks to run
                // debug!("Timeout waiting for message from provider: {}, yielding to other tasks", provider_addr);

                // Explicitly yield to allow other tasks to run
                tokio::task::yield_now().await;

                continue;
            }
        };

        // Process the message
        match msg_result {
            Some(Ok(msg)) => {
                match msg {
                    Message::Text(text) => {
                        // Parse and handle client message
                        match handle_client_message(&text, &provider_addr, session_manager.clone())
                            .await
                        {
                            Ok(response) => {
                                if let Some(resp) = response {
                                    // Send the response - we need to lock the connections map again
                                    let mut conns = connections.lock().await;
                                    if let Some(conn) = conns.get_mut(&provider_addr) {
                                        if let Err(e) = conn.send_raw(Message::Text(resp.into())).await {
                                            error!(
                                                "Failed to send response to {}: {}",
                                                provider_addr, e
                                            );
                                        } else {
                                            info!(
                                                "Successfully sent heartbeat_ack to {}",
                                                provider_addr
                                            );
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                // log the message
                                error!("Failed to handle message: {} from {}: {}", &text ,provider_addr, e);

                                // Send error response
                                let mut conns = connections.lock().await;
                                if let Some(conn) = conns.get_mut(&provider_addr) {
                                    let _ = conn
                                        .send_message(
                                            "error",
                                            json!({
                                                "message": format!("Error: {}", e),
                                                "code": "MESSAGE_PROCESSING_ERROR"
                                            }),
                                        )
                                        .await;
                                }
                            }
                        }
                    }
                    Message::Binary(_) => {
                        // We don't handle binary messages in this example
                        debug!("Received binary message from {}", provider_addr);
                    }
                    Message::Ping(data) => {
                        // Respond to ping with pong
                        let mut conns = connections.lock().await;
                        if let Some(conn) = conns.get_mut(&provider_addr) {
                            if let Err(e) = conn.send_raw(Message::Pong(data)).await {
                                error!("Failed to send pong to {}: {}", provider_addr, e);
                            }
                        }
                    }
                    Message::Pong(_) => {
                        // We received a pong response, nothing to do
                        debug!("Received pong from {}", provider_addr);
                    }
                    Message::Close(_) => {
                        info!("Received close message from {}", provider_addr);
                        // Remove connection - we need to lock the connections map again
                        {
                            let mut conns = connections.lock().await;
                            conns.remove(&provider_addr);

                            // Log the current count of connected providers after removal
                            let count = conns.len();
                            info!("Provider {} disconnected. Total connected providers: {}", provider_addr, count);
                        }
                        break;
                    }
                    Message::Frame(_) => {
                        // Raw frames are not handled
                    }
                }
            }
            Some(Err(e)) => {
                error!("Error receiving message from {}: {}", provider_addr, e);

                // Remove connection on error - we need to lock the connections map again
                {
                    let mut conns = connections.lock().await;
                    conns.remove(&provider_addr);

                    // Log the current count of connected providers after removal
                    let count = conns.len();
                    info!("Provider {} disconnected due to error. Total connected providers: {}", provider_addr, count);
                }
                break;
            }
            None => {
                // Connection closed
                info!("Connection closed for provider: {}", provider_addr);

                // Remove connection - we need to lock the connections map again
                {
                    let mut conns = connections.lock().await;
                    conns.remove(&provider_addr);

                    // Log the current count of connected providers after removal
                    let count = conns.len();
                    info!("Provider {} connection closed. Total connected providers: {}", provider_addr, count);
                }
                break;
            }
        }
    }
}

/// Handle a client message and return an optional response
async fn handle_client_message(
    message: &str,
    provider_addr: &str,
    session_manager: Arc<SessionManager>,
) -> Result<Option<String>, anyhow::Error> {
    let parsed: serde_json::Value = serde_json::from_str(message)?;
    // info!("Received message from {}: {}", provider_addr, message);

    let msg_type = parsed["type"]
        .as_str()
        .ok_or_else(|| anyhow::anyhow!("Missing message type"))?;
    // info!("Processing message type: {} from provider {}", msg_type, provider_addr);

    match msg_type {
        "heartbeat" => {
            // Just respond with a heartbeat acknowledge
            Ok(Some(
                json!({
                    "type": "heartbeat_ack",
                    "timestamp": chrono::Utc::now().timestamp()
                })
                .to_string(),
            ))
        }
        "authenticate" => {
            // Authentication is handled separately during connection setup
            // This is just a placeholder for any post-connection authentication requests
            Ok(Some(
                json!({
                    "type": "auth_response",
                    "message": "Already authenticated as provider",
                    "provider_addr": provider_addr
                })
                .to_string(),
            ))
        }
        "get_sessions" => {
            // Request for current sessions
            let active_sessions = session_manager
                .get_active_sessions_for_provider(provider_addr)
                .await?;
            info!("Found {} active sessions for provider: {}", active_sessions.len(), provider_addr);

            // Convert sessions to JSON values
            let mut session_updates = Vec::new();
            for session in active_sessions {
                let session_data = serde_json::to_value(&session)?;
                session_updates.push(session_data);
            }

            Ok(Some(
                json!({
                    "type": "sessions_response",
                    "sessions": session_updates
                })
                .to_string(),
            ))
        }
        // Additional message types can be handled here
        _ => {
            // Unknown message type
            Err(anyhow::anyhow!("Unknown message type: {}", msg_type))
        }
    }
}

// Helper function to authenticate a connection
async fn authenticate_connection(
    connection: &mut Connection,
    config: &Config,
) -> Result<String, anyhow::Error> {
    // Get the authentication endpoint from config
    info!("Start authentication process");
    let auth_endpoint = config.auth_endpoint.clone();

    // Create HTTP client
    let client = reqwest::Client::new();

    // Wait for authentication message
    while let Some(msg_result) = connection.receive().await {
        let msg = msg_result?;

        // We only care about text messages for authentication
        if let Message::Text(text) = msg {
            // Parse the message
            let parsed: serde_json::Value = serde_json::from_str(&text)?;

            // Check if it's an authentication message
            if parsed["type"] == "authenticate" {
                let access_token = parsed["access_token"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing access token"))?;

                // Create request body
                let auth_body = json!({
                    "access_token": access_token,
                    "refresh_token": access_token
                });

                // Make POST request to authentication endpoint
                let response = client.post(&auth_endpoint).json(&auth_body).send().await?;
                debug!("Sending authentication request to: {}", auth_endpoint);

                // Check response status
                if response.status().is_success() {
                    // Parse response body
                    let response_body: serde_json::Value = response.json().await?;

                    // Extract provider address from response
                    let provider_addr = response_body["user_addr"]
                        .as_str()
                        .ok_or_else(|| anyhow::anyhow!("Provider address not found in response"))?
                        .to_string();

                     // Send authentication success message
                    connection.send_message("auth_success", json!({
                        "provider_addr": provider_addr
                    })).await?;

                    debug!("Provider authenticated successfully: {}", provider_addr);
                    return Ok(provider_addr);
                } else {
                    // Authentication failed
                    let error_msg = format!("Authentication failed: HTTP {}", response.status());
                    error!("{}", error_msg);
                    return Err(anyhow::anyhow!(error_msg));
                }
            }
        }
    }

    // If we reach here, the connection was closed before authentication
    Err(anyhow::anyhow!("Connection closed before authentication"))
}
