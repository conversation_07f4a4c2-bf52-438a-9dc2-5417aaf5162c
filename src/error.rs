use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum ServerError {
    #[error("WebSocket error: {0}")]
    WebSocketError(String),
    
    #[error("Session error: {0}")]
    SessionError(String),
    
    #[error("Authentication error: {0}")]
    AuthError(String),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("Internal server error: {0}")]
    InternalError(String),
}

#[derive(Error, Debug)]
pub enum StorageError {
    #[error("Redis error: {0}")]
    RedisError(String),
    
    #[error("Session not found: {0}")]
    SessionNotFound(String),
    
    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("InternalError error: {0}")]
    InternalError(String),
}

#[derive(Error, Debug)]
pub enum MessagingError {
    #[error("RabbitMQ error: {0}")]
    RabbitMQError(String),
    
    #[error("Message processing error: {0}")]
    ProcessingError(String),
}