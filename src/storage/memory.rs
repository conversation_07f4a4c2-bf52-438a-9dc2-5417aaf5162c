//  store and retrieve session data in memory
use crate::{error::StorageError, session::models::Session};
use anyhow::{Context, Result};
use std::{
    collections::{HashMap, HashSet},
    sync::RwLock,
};
use tracing::{debug, warn};

/// In-memory storage for Session data
pub struct MemorySessionStore {
    /// session_hash -> Session
    sessions: RwLock<HashMap<String, Session>>,

    /// provider_addr -> Set of session_hash
    provider_sessions: RwLock<HashMap<String, HashSet<String>>>,
}

impl MemorySessionStore {
    pub fn new() -> Self {
        Self {
            sessions: RwLock::new(HashMap::new()),
            provider_sessions: RwLock::new(HashMap::new()),
        }
    }

    /// Store a session in memory
    pub fn store_session(&self, session: Session) -> Result<()> {
        // First, update the provider index
        {
            let mut provider_idx = self.provider_sessions.write().map_err(|_| {
                StorageError::InternalError("Failed to acquire write lock on provider index".into())
            })?;

            provider_idx
                .entry(session.provider_addr.clone())
                .or_insert_with(HashSet::new)
                .insert(session.session_hash.clone());
        }
        // Then, store the session itself
        {
            let mut sessions = self.sessions.write().map_err(|_| {
                StorageError::InternalError("Failed to acquire write lock on sessions".into())
            })?;

            sessions.insert(session.session_hash.clone(), session);
        }

        debug!("Session stored in memory");
        Ok(())
    }

    /// Get a session by its hash
    pub fn get_session(&self, session_hash: &str) -> Result<Option<Session>> {
        let sessions = self.sessions.read().map_err(|_| {
            StorageError::InternalError("Failed to acquire read lock on sessions".into())
        })?;

        Ok(sessions.get(session_hash).cloned())
    }

    /// Update an existing session
    pub fn update_session(&self, session: Session) -> Result<()> {
        let mut sessions = self.sessions.write()
            .map_err(|_| StorageError::InternalError("Failed to acquire write lock on sessions".into()))?;
            
        if !sessions.contains_key(&session.session_hash) {
            warn!("Attempted to update non-existent session");
            return Err(StorageError::SessionNotFound(session.session_hash).into());
        }
        
        sessions.insert(session.session_hash.clone(), session);
        debug!("Session updated in memory");
        Ok(())
    }


    /// Remove a session
    pub fn remove_session(&self, session_hash: &str) -> Result<()> {
        // First, get the session to retrieve provider and client info
        let session = {
            let sessions = self.sessions.read()
                .map_err(|_| StorageError::InternalError("Failed to acquire read lock on sessions".into()))?;
                
            match sessions.get(session_hash) {
                Some(session) => session.clone(),
                None => return Ok(()), // Session already gone, nothing to do
            }
        };
        
        // Update the provider index
        {
            let mut provider_idx = self.provider_sessions.write()
                .map_err(|_| StorageError::InternalError("Failed to acquire write lock on provider index".into()))?;
                
            if let Some(hashes) = provider_idx.get_mut(&session.provider_addr) {
                hashes.remove(session_hash);
                
                // If this was the last session for this provider, remove the entry
                if hashes.is_empty() {
                    provider_idx.remove(&session.provider_addr);
                }
            }
        }
        
        // Finally, remove the session itself
        {
            let mut sessions = self.sessions.write()
                .map_err(|_| StorageError::InternalError("Failed to acquire write lock on sessions".into()))?;
                
            sessions.remove(session_hash);
        }
        
        debug!("Session removed from memory");
        Ok(())
    }

    /// Get all active sessions in memory
    pub fn get_all_sessions(&self) -> Result<Vec<Session>> {
        let sessions = self.sessions.read()
            .map_err(|_| StorageError::InternalError("Failed to acquire read lock on sessions".into()))?;
            
        Ok(sessions.values().cloned().collect())
    }

     /// Count total sessions
     pub fn count_sessions(&self) -> Result<usize> {
        let sessions = self.sessions.read()
            .map_err(|_| StorageError::InternalError("Failed to acquire read lock on sessions".into()))?;
            
        Ok(sessions.len())
    }

}
