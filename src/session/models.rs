use num_derive::FromPrimitive;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

#[derive(Debug, Clone, FromPrimitive, Serialize, Deserialize, PartialEq,ToSchema)]
pub enum SessionStatus {
    Active,
    Finished,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    pub session_hash: String,
    pub provider_addr: String,
    pub client_addr: String,
    /// szabo
    pub rate_per_kb_v2: i64,
    /// szabo
    pub rate_per_kb: i64,
    pub handshake_at: Option<i64>,
    pub end_at: Option<i64>,
    /// epoch seconds
    pub duration: Option<i64>,
    /// in kilobytes
    pub bandwidth_usage: Option<i64>,
    /// szabo
    pub duration_fee: i64,
    /// szabo
    pub bandwidth_fee: i64,
    /// szabo
    pub total_fee: i64,
    pub status: SessionStatus,
}