use anyhow::Result;
use chrono::Utc;
use std::collections::{HashMap, HashSet};
use tokio::sync::Mutex;
use tracing::{debug, error, info, warn};

use crate::messaging::events::{EphemeralSession, SessionEvent};
use crate::{
    messaging::events::SessionStats,
    storage::memory::MemorySessionStore,
};

use super::models::{Session, SessionStatus};

// Type to hold aggregated session updates for a provider
pub struct AggregatedUpdates {
    pub updated_sessions: Vec<Session>,
    // No longer including stats in the response
}

pub struct SessionManager {
    memory_store: MemorySessionStore,
    // redis_client: RedisClient,
    /// provider_addr -> set of updated session_hashes
    pending_updates: Mutex<HashMap<String, HashSet<String>>>,
}

impl SessionManager {
    pub fn new() -> Self {
        Self {
            memory_store: MemorySessionStore::new(),
            // stats_cache: Mutex::new(HashMap::new()),
            pending_updates: Mutex::new(HashMap::new()),
        }
    }

    pub async fn handle_session_created(&self, event: &SessionEvent) -> Result<()> {
        if let SessionEvent::SessionCreated(extra) = event {
            let session = self.ephemeral_to_session(&extra.session);

            // Store in memory
            self.memory_store.store_session(session.clone())?;
            debug!("Session is stored in memory: {}", session.session_hash);

            // Store in Redis
            // self.redis_client.store_session(&session, 86400).await?;
            // debug!("Session is stored in Redis: {}", session.session_hash);

            // Mark this session as updated for the provider
            self.mark_session_updated(&session.provider_addr, &session.session_hash)
                .await;
            debug!("Session is marked as updated: {}", session.session_hash);

            // debug!("Created new session: {}", session.session_hash);
        }

        Ok(())
    }

    pub async fn handle_session_terminated(&self, event: &SessionEvent) -> Result<()> {
        if let SessionEvent::SessionTerminated(extra) = event {
            let session = self.ephemeral_to_session(&extra.session);

            // Get the existing session to update it properly
            if let Some(mut existing_session) =
                self.memory_store.get_session(&session.session_hash)?
            {
                // Update only the relevant fields
                existing_session.end_at = session.end_at;
                existing_session.duration = session.duration;
                existing_session.bandwidth_usage = session.bandwidth_usage;
                existing_session.status = SessionStatus::Finished;

                // Update in memory
                self.memory_store.update_session(existing_session.clone())?;

                // TODO: Update in Redis
                // self.redis_client
                //     .store_session(&existing_session, 86400)
                //     .await?;

                // Mark this session as updated for the provider
                self.mark_session_updated(
                    &existing_session.provider_addr,
                    &existing_session.session_hash,
                )
                .await;

                debug!("Terminated session: {}", existing_session.session_hash);
            } else {
                // Session doesn't exist yet, create it
                self.memory_store.store_session(session.clone())?;

                // Store in Redis
                // self.redis_client.store_session(&session, 86400).await?;

                // Mark this session as updated for the provider
                self.mark_session_updated(&session.provider_addr, &session.session_hash)
                    .await;

                debug!("Created terminated session: {}", session.session_hash);
            }
        }

        Ok(())
    }

    pub async fn handle_session_stats(&self, stats: &SessionStats) -> Result<()> {
        debug!("Received stats update for session: {}", stats.session_hash);
        // No longer need to update stats cache
        // {
        //     let mut stats_cache = self.stats_cache.lock().await;
        //     stats_cache.insert(stats.session_hash.clone(), stats.clone());
        //     debug!("Stats cache updated for session: {} ", stats.session_hash);
        // }

        // Find the associated session to get the provider_addr and update bandwidth_usage
        match self.memory_store.get_session(&stats.session_hash) {
            Ok(Some(mut session)) => {
                debug!("Found session in memory store: {} with status: {:?}", stats.session_hash, session.status);

                // Update bandwidth_usage by adding stats.download
                let current_usage = session.bandwidth_usage.unwrap_or(0);
                session.bandwidth_usage = Some(current_usage + stats.download as i64);
                debug!("Updated bandwidth_usage for session {}: {} + {} = {}",
                stats.session_hash, current_usage, stats.download, session.bandwidth_usage.unwrap_or(0));

                // Update total_fee based on bandwidth_usage * rate_per_kb
                if let Some(bandwidth) = session.bandwidth_usage {
                    session.total_fee = bandwidth * session.rate_per_kb;
                    debug!("Updated total_fee for session {}: {} * {} = {}",
                           stats.session_hash, bandwidth, session.rate_per_kb, session.total_fee);
                }


                // Update the session in memory store
                if let Err(e) = self.memory_store.update_session(session.clone()) {
                    warn!("Failed to update session with new bandwidth_usage and total_fee: {}", e);
                }

                // Always mark stats updates for update
                debug!("Marking session for update regardless of status: {}", stats.session_hash);
                self.mark_session_updated(&session.provider_addr, &stats.session_hash).await;
                debug!("Session marked for update: {}", stats.session_hash);
            },
            Ok(None) => {
                debug!("Session not found in memory store: {}", stats.session_hash);
                // Session not found, we need to create a placeholder session
                // This is a workaround for when we receive stats before the session creation event
                debug!("Creating placeholder session for stats: {}", stats.session_hash);

                // Extract provider address from login_session_id by the first 42 characters
                if !stats.login_session_id.is_empty() {
                    // Take up to the first 42 characters as the provider address
                    let provider_addr = &stats.login_session_id[..42.min(stats.login_session_id.len())];
                    debug!("Extracted provider address from login_session_id: '{}' (len: {})",
                           provider_addr, provider_addr.len());

                    // Create a placeholder session
                    let bandwidth = stats.download as i64;
                    let rate_per_kb = 50; // Default rate
                    let total_fee = bandwidth * rate_per_kb;

                    let placeholder_session = Session {
                        session_hash: stats.session_hash.clone(),
                        provider_addr: provider_addr.to_string(),
                        client_addr: "unknown".to_string(),
                        rate_per_kb_v2: 0,
                        rate_per_kb: rate_per_kb,
                        handshake_at: None,
                        end_at: None,
                        duration: None,
                        bandwidth_usage: Some(bandwidth), // Initialize with current download value
                        duration_fee: 0,
                        bandwidth_fee: 0,
                        total_fee: total_fee,
                        status: SessionStatus::Active,
                    };

                    debug!("Calculated total_fee for placeholder session {}: {} * {} = {}",
                           stats.session_hash, bandwidth, rate_per_kb, total_fee);

                    // Store the placeholder session
                    if let Err(e) = self.memory_store.store_session(placeholder_session.clone()) {
                        warn!("Failed to store placeholder session: {}", e);
                    } else {
                        debug!("Stored placeholder session: {}", stats.session_hash);
                        // Mark the session for update
                        self.mark_session_updated(&provider_addr.to_string(), &stats.session_hash).await;
                        debug!("Placeholder session marked for update: {}", stats.session_hash);
                    }
                } else {
                    warn!("Empty login_session_id in stats for session: {}", stats.session_hash);
                }
            },
            Err(e) => {
                warn!("Error getting session from memory store: {}", e);
            }
        }

        Ok(())
    }

    pub async fn cleanup_finished_sessions(&self) -> Result<()> {
        // clean up session in memory
        let finished_sessions = self.memory_store.get_all_sessions()?.into_iter()
            .filter(|s| s.status == SessionStatus::Finished)
            .collect::<Vec<_>>();

        for session in finished_sessions.clone() {
            self.memory_store.remove_session(&session.session_hash)?;
        }

        // clean up session in redis
        // for session in finished_sessions {
        //     self.redis_client.remove_session(&session.session_hash).await?;
        // }

        Ok(())
    }

    pub async fn get_aggregated_updates(&self) -> HashMap<String, AggregatedUpdates> {
        let mut result = HashMap::new();
        // debug!("Getting aggregated updates");

        // Get and clear pending updates
        let pending = {
            let mut updates = self.pending_updates.lock().await;
            // Log with more detailed format to avoid truncation
            debug!("Current pending updates before clearing: {}",
                updates.iter()
                    .map(|(addr, sessions)| format!("Provider '{}' (len: {}) -> {:?}", addr, addr.len(), sessions))
                    .collect::<Vec<_>>()
                    .join(", ")
            );
            std::mem::take(&mut *updates)
        };
        // Log with more detailed format to avoid truncation
        debug!("Processing pending updates: {}",
            pending.iter()
                .map(|(addr, sessions)| format!("Provider '{}' (len: {}) -> {:?}", addr, addr.len(), sessions))
                .collect::<Vec<_>>()
                .join(", ")
        );

        // For each provider with updates
        for (provider_addr, session_hashes) in pending {
            debug!("Processing updates for provider: '{}' (len: {}) with {} sessions",
                   provider_addr, provider_addr.len(), session_hashes.len());
            let mut provider_updates = AggregatedUpdates {
                updated_sessions: Vec::new(),
            };

            // Get session details
            for session_hash in session_hashes {
                debug!("Processing session: {}", session_hash);
                // Get session details
                match self.memory_store.get_session(&session_hash) {
                    Ok(Some(session)) => {
                        debug!("Found session in memory: {} with status: {:?}", session_hash, session.status);
                        provider_updates.updated_sessions.push(session);
                    },
                    Ok(None) => {
                        warn!("Failed to get session {} for provider {} - session not found", session_hash, provider_addr);
                    },
                    Err(e) => {
                        warn!("Error getting session {} for provider {}: {}", session_hash, provider_addr, e);
                    }
                }
                // No longer getting or storing stats
            }

            // Only add if there are actual updates
            if !provider_updates.updated_sessions.is_empty() {
                debug!("Adding updates for provider '{}' (len: {}): {} sessions",
                      provider_addr, provider_addr.len(), provider_updates.updated_sessions.len());
                result.insert(provider_addr, provider_updates);
            } else {
                warn!("No actual updates to send for provider '{}' (len: {}) despite being in pending updates",
                      provider_addr, provider_addr.len());
            }
        }

        debug!("Returning aggregated updates for {} providers", result.len());
        result
    }

    // pub async fn get_session_stats(&self, session_hashes: &[String]) -> HashMap<String, SessionStats> {
    //     let stats_cache = self.stats_cache.lock().await;
    //     let mut result = HashMap::new();

    //     for hash in session_hashes {
    //         if let Some(stats) = stats_cache.get(hash) {
    //             result.insert(hash.clone(), stats.clone());
    //         }
    //     }

    //     result
    // }

    pub async fn get_active_sessions_for_provider(
        &self,
        provider_addr: &str,
    ) -> Result<Vec<Session>>{
        let sessions = self.memory_store.get_all_sessions()?;
        debug!("All sessions retrieved from memory: {:?}", sessions.clone());
        Ok(sessions
            .into_iter()
            .filter(|s| s.provider_addr == provider_addr && s.status == SessionStatus::Active)
            .collect())
    }

    /// Mark a session as updated for a provider
    //
    async fn mark_session_updated(&self, provider_addr: &str, session_hash: &str) {
        debug!("Marking session as updated for provider: '{}' (len: {}) session: {}",
               provider_addr, provider_addr.len(), session_hash);
        let mut updates = self.pending_updates.lock().await;
        updates
            .entry(provider_addr.to_string())
            .or_insert_with(HashSet::new)
            .insert(session_hash.to_string());

        // Log with more detailed format to avoid truncation
        debug!("Session marked as updated. Current pending updates: {}",
            updates.iter()
                .map(|(addr, sessions)| format!("Provider '{}' (len: {}) -> {:?}", addr, addr.len(), sessions))
                .collect::<Vec<_>>()
                .join(", ")
        );
    }

    /// Convert EphemeralSession to our internal Session model
    ///
    fn ephemeral_to_session(&self, ephemeral: &EphemeralSession) -> Session {
        Session {
            session_hash: ephemeral.hash.clone(),
            provider_addr: ephemeral.peer_addr.clone(),
            client_addr: ephemeral.client_addr.clone(),
            rate_per_kb_v2: ephemeral.rate_per_kb_v2,
            rate_per_kb: ephemeral.rate_per_kb,
            handshake_at: Some(ephemeral.handshaked_at),
            end_at: if ephemeral.end_at > 0 {
                Some(ephemeral.end_at)
            } else {
                None
            },
            duration: if ephemeral.end_at > 0 {
                Some(ephemeral.end_at - ephemeral.handshaked_at)
            } else {
                Some(Utc::now().timestamp() - ephemeral.handshaked_at)
            },
            bandwidth_usage: Some(ephemeral.bandwidth_usage),
            duration_fee: 0, // These will be filled in by application logic
            bandwidth_fee: 0,
            total_fee: {
                let fee = ephemeral.bandwidth_usage * ephemeral.rate_per_kb;
                debug!("Calculated total_fee for session {}: {} * {} = {}",
                       ephemeral.hash, ephemeral.bandwidth_usage, ephemeral.rate_per_kb, fee);
                fee
            }, // Calculate total_fee based on bandwidth_usage * rate_per_kb
            status: if ephemeral.end_at > 0 && ephemeral.end_at > ephemeral.handshaked_at + 1 {
                debug!("Session marked as Finished: end_at={}, handshaked_at={}", ephemeral.end_at, ephemeral.handshaked_at);
                SessionStatus::Finished
            } else {
                debug!("Session marked as Active: end_at={}, handshaked_at={}", ephemeral.end_at, ephemeral.handshaked_at);
                SessionStatus::Active
            },
        }
    }
}
